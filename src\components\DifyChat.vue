<template>
  <div class="dify-chat-wrapper" :class="{ 'page-mode': displayMode === 'page' }">

    <!-- 聊天气泡按钮 (仅弹窗模式显示) -->
    <div v-if="displayMode === 'popup' && !isOpen" class="chat-bubble" @click="toggleChat" :style="bubbleStyle"
      title="点击打开聊天">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="currentColor" />
      </svg>
    </div>

    <!-- 聊天弹窗/页面 -->
    <div v-if="displayMode === 'page' || isOpen" class="chat-popup" :class="{
      'expanded': isExpanded,
      'with-history': showHistoryList,
      'page-mode': displayMode === 'page',
      'mobile-flat': isMobile
    }" :style="displayMode === 'page' ? pageStyle : popupStyle">

      <!-- 移动端顶部导航栏 -->
      <div v-if="isMobile" class="mobile-top-nav">
        <div class="nav-left">
          <button class="nav-btn" @click="toggleMobileMenu" :class="{ 'active': showMobileMenu }">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="nav-center">
          <span class="nav-title">{{ currentConversationTitle || 'Chat Prompt' }}</span>
        </div>
        <div class="nav-right">
          <button class="nav-btn" @click="startNewConversation">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端功能菜单 -->
      <div v-if="isMobile && showMobileMenu" class="mobile-menu-overlay" @click="showMobileMenu = false">
        <div class="mobile-menu" @click.stop>
          <div class="menu-header">
            <span class="menu-title">功能菜单</span>
            <button class="menu-close" @click="showMobileMenu = false">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
          <div class="menu-items">
            <button class="menu-item" @click="startNewConversation; showMobileMenu = false">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span>新对话</span>
            </button>
            <button class="menu-item" @click="toggleHistoryList; showMobileMenu = false">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span>历史记录</span>
            </button>
            <button class="menu-item" @click="handleBack; showMobileMenu = false">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2-5L9 12l5 5M2 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span>智能体页面</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 页面右上角返回按钮 -->
      <div v-if="displayMode === 'page' && !isMobile" class="page-back-button">
        <button class="page-back-btn" @click="handleBack" title="返回">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          智能体
        </button>
      </div>

      <!-- 主体内容区域 -->
      <div class="chat-body">
        <!-- 历史对话列表 -->
        <div v-if="showHistoryList" class="history-sidebar">
          <div class="chat-title">
            <div class="message-avatar">
              <img :src="aiImg" alt="AI头像">
            </div>
            <span class="title-text">{{ title }}</span>
            <button class="history-close-btn" @click="toggleHistoryList" title="关闭历史对话">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" width="26"
                height="26" stroke="currentColor" class=" size-6 text-black dark:text-white ">
                <g opacity="0.8">
                  <path
                    d="M3 17.0048V6.99666C3 4.78752 4.7865 3 6.99563 3H17.0037C19.2129 3 21 4.78757 21 6.99671V17.0048C21 19.214 19.2129 21 17.0037 21H6.99563C4.7865 21 3 19.2139 3 17.0048Z"
                    stroke-linejoin="round"></path>
                  <path d="M15 3V21" stroke-linecap="round" stroke-linejoin="round"></path>
                  <path d="M8 10L10 12L8 14" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
              </svg>
            </button>
          </div>
          <div class="new-chat-btn" @click="startNewConversation">
            开启新对话
            <button title="新建对话">
              <svg width="14" height="14" viewBox="0 0 16 16" fill="none">
                <path d="M8 2v12M2 8h12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </button>
          </div>
          <div class="history-header">
            <h4>历史对话</h4>
          </div>
          <div class="history-list" ref="historyList" @scroll="handleHistoryScroll">
            <!-- 加载状态 -->
            <div v-if="isLoadingHistory" class="history-loading">
              <div class="loading-spinner"></div>
              <span>加载历史对话中...</span>
            </div>

            <!-- 历史对话列表 -->
            <template v-for="(group, groupIndex) in groupedConversations">
              <div class="history-group-header" v-if="group.conversations.length > 0" :key="'group-header-' + groupIndex">{{ group.label }}</div>
              <div v-for="conversation in group.conversations" :key="conversation.id" class="history-item"
                :class="{ 'active': conversationId === conversation.id }" @click="loadConversation(conversation.id)">
                <div class="history-item-content">
                  <div class="history-title">{{ conversation.title }}</div>
                  <div class="history-time">{{ formatHistoryTime(conversation.timestamp) }}</div>
                </div>
                <button class="delete-conversation-btn" @click.stop="deleteConversation(conversation.id)" title="删除对话">
                  <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                    <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  </svg>
                </button>
              </div>
            </template>

            <!-- 加载更多状态 -->
            <div v-if="isLoadingMoreHistory" class="history-loading-more">
              <div class="loading-spinner"></div>
              <span>加载更多对话中...</span>
            </div>

            <!-- 空状态 -->
            <div v-if="!isLoadingHistory && historyConversations.length === 0" class="history-empty">
              <div class="empty-icon">💬</div>
              <div class="empty-text">暂无历史对话</div>
              <div class="empty-hint">开始新对话来创建历史记录</div>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-if="!hasMoreHistory && historyConversations.length > 20 && !isLoadingHistory"
              class="history-no-more">
              <span>没有更多对话了</span>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <!-- 头部 (仅桌面端显示) -->
          <div v-if="!isMobile" class="chat-header">
            <div class="header-left">
              <!-- 历史对话按钮 -->
              <button v-if="!showHistoryList" class="history-btn" @click="toggleHistoryList"
                :title="showHistoryList ? '隐藏历史对话' : '显示历史对话'">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" width="16"
                  height="16" stroke="currentColor" class=" size-6 text-black dark:text-white ">
                  <g opacity="0.8">
                    <path
                      d="M3 17.0048V6.99666C3 4.78752 4.7865 3 6.99563 3H17.0037C19.2129 3 21 4.78757 21 6.99671V17.0048C21 19.214 19.2129 21 17.0037 21H6.99563C4.7865 21 3 19.2139 3 17.0048Z"
                      stroke-linejoin="round"></path>
                    <path d="M15 3V21" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M8 10L10 12L8 14" stroke-linecap="round" stroke-linejoin="round"></path>
                  </g>
                </svg>
              </button>
              <button v-if="!showHistoryList" class="history-btn" @click="startNewConversation" title="开启新对话">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M8 2v12M2 8h12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>
              </button>
            </div>
            <!-- 当前聊天标题 -->
            <div class="current-chat-title">
              <span>{{ currentConversationTitle || '新对话' }}</span>
            </div>
            <div class="header-actions">
              <!-- 桌面端显示放大/缩小按钮 -->
              <button v-if="!isMobile && displayMode === 'popup'" class="expand-btn" @click="toggleExpand"
                :title="isExpanded ? '缩小' : '放大'">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path v-if="!isExpanded"
                    d="M2 2H6V4H4V6H2V2ZM10 2H14V6H12V4H10V2ZM2 14V10H4V12H6V14H2ZM12 10H14V14H10V12H12V10Z"
                    fill="currentColor" />
                  <path v-else d="M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z" fill="currentColor" />
                </svg>
              </button>
              <button v-if="displayMode === 'popup'" class="close-btn" @click="toggleChat">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>
              </button>
            </div>
          </div>

          <!-- 开场白 -->

          <div v-if="appParameters.opening_statement && messages.length === 1 && !conversationId"
            class="opening-statement" :class="{ 'popup-mode': displayMode === 'popup' && !isExpanded }">
            <div class="opening-header">
              <div class="message-avatar">
                <img :src="aiImg" alt="AI头像">
              </div>
              <div class="message-name">您好，我是{{ appInfo?.name }}，很高兴为您服务！</div>
            </div>
            <p class="opening-text" v-html="appParameters.opening_statement"></p>
            <!-- 推荐问题列表 -->
            <div
              v-if="messages.length === 1 && messages[0].type === 'assistant' && appParameters.suggested_questions && appParameters.suggested_questions.length > 0"
              class="suggested-questions">
              <div class="questions-title">您可能想问：</div>
              <div class="questions-list">
                <button v-for="(question, index) in appParameters.suggested_questions" :key="index"
                  class="question-item" @click="handleSuggestedQuestion(question)">
                  <span class="question-icon">💬</span>
                  <span class="question-text">{{ question }}</span>
                </button>
              </div>
            </div>
          </div>
          <!-- 消息列表 -->
          <div v-else class="chat-messages" ref="messagesContainer" @scroll="handleScroll">
            <div :class="[ displayMode === 'page'&& !isMobile?'chat-wrapper':'chat-wrapper-mobile']">
              <div v-for="(message, index) in messages" :key="index" :class="['message', message.type, {
                'streaming': message.isStreaming,
                'thinking': message.isThinking
              }]">
                <!-- 添加头像显示 -->
                <div class="message-avatar" v-if="message.type === 'assistant' && !isMobile">
                  <img :src="aiImg" alt="AI头像">
                </div>
                <div class="message-content">
                  <!-- 思考过程显示 -->
                  <div v-if="(message.thinking && message.thinking.trim()) || message.isThinking"
                    class="thinking-section">
                    <div class="thinking-header" @click="toggleThinking(index)">
                      <span class="thinking-icon">🤔</span>
                      <span class="thinking-title">
                        AI思考过程
                        <span v-if="message.isThinking" class="thinking-status">(思考中...)</span>
                        <span v-else-if="message.thinking">({{ message.thinking.length }}字符)</span>
                      </span>
                      <span class="thinking-toggle" :class="{ 'expanded': message.showThinking }">
                        {{ message.showThinking ? '▼' : '▶' }}
                      </span>
                    </div>
                    <div v-if="message.showThinking" class="thinking-content">
                      <div v-if="message.thinking && message.thinking.trim()" class="thinking-text markdown-body"
                        v-html="formatMessage(message.thinking)"></div>
                      <div v-else-if="message.isThinking" class="thinking-placeholder">
                        <div class="thinking-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                        <span class="thinking-placeholder-text">AI正在思考中，请稍候...</span>
                      </div>
                    </div>
                  </div>

                  <!-- 文件列表 -->
                  <div v-if="message.files && message.files.length > 0" class="message-files">
                    <div v-for="(file, fileIndex) in message.files" :key="fileIndex" class="message-file">
                      <svg width="14" height="14" viewBox="0 0 16 16" fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                      </svg>
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size">({{ formatFileSize(file.size) }})</span>
                    </div>
                  </div>

                  <!-- 消息内容 -->
                  <div class="message-text-container">
                    <!-- Loading状态指示器 - 当没有内容且没有思考过程时显示 -->
                    <div v-if="!message.content && !message.thinking && !message.isComplete && !message.isPaused" class="loading-indicator">
                      <span class="thinking-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </span>
                      <!-- <span class="thinking-label">AI正在回复</span> -->
                    </div>

                    <!-- 暂停状态提示 -->
                    <div v-if="message.isPaused" class="paused-indicator">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" fill="currentColor"/>
                      </svg>
                      <span class="paused-text">回复已暂停</span>
                    </div>

                    <!-- 正常消息内容 -->
                    <div v-else-if="message.content" class="message-text markdown-body" ref="markdownBody"
                      :class="[{ 'bot-body': message.type === 'assistant' }]" v-html="message.content">
                    </div>

                    <!-- 流式消息的光标动画 -->
                    <!-- <span v-if="message.isStreaming && !message.isThinking && message.content" class="streaming-cursor">|</span> -->
                  </div>


                  <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="isLoading && !currentStreamingMessage" class="message assistant">
                <div class="message-content">
                  <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI免责声明 -->
          <div class="ai-disclaimer" :class="{ 'mobile': isMobile }">
            <span>AI生成的内容可能不准确，请自行判断</span>
          </div>

          <!-- 输入框 -->
          <div class="chat-input"
            :class="{ 'drag-over': dragOver, 'opening': appParameters.opening_statement && messages.length === 1 && !conversationId && displayMode === 'page'&& !isMobile,'chat-input-mobile':isMobile }"
            @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop">

            <!-- 已上传文件列表 -->
            <div v-if="uploadedFiles.length > 0" class="uploaded-files">
              <div class="files-header">
                <span class="files-count">已上传文件 ({{ uploadedFiles.length }}/{{ fileUploadLimits?.maxFiles || 3
                  }})</span>
              </div>
              <div class="files-list">
                <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                  <div class="file-info">
                    <div class="file-icon-wrapper" :class="findFileType(file.extension)">
                      <!-- 文档类型图标 -->
                      <svg v-if="findFileType(file.extension) === 'document'" width="14" height="14" viewBox="0 0 16 16"
                        fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                        <path d="M4 8H12M4 11H10" stroke="currentColor" stroke-width="1" stroke-linecap="round" />
                      </svg>

                      <!-- 图片类型图标 -->
                      <svg v-else-if="findFileType(file.extension) === 'image'" width="14" height="14"
                        viewBox="0 0 16 16" fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                        <circle cx="5.5" cy="8.5" r="1.5" fill="white" />
                        <path d="M4 12L6 10L8 12L12 8V12H4V12Z" fill="white" />
                      </svg>

                      <!-- 音频类型图标 -->
                      <svg v-else-if="findFileType(file.extension) === 'audio'" width="14" height="14"
                        viewBox="0 0 16 16" fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                        <path d="M8 8V12M6 10V12M10 6V12" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                      </svg>

                      <!-- 视频类型图标 -->
                      <svg v-else-if="findFileType(file.extension) === 'video'" width="14" height="14"
                        viewBox="0 0 16 16" fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                        <path d="M6 8.5V11.5L9.5 10L6 8.5Z" fill="white" />
                      </svg>

                      <!-- 默认文件图标 -->
                      <svg v-else width="14" height="14" viewBox="0 0 16 16" fill="none" class="file-icon">
                        <path d="M9 1H3C2.4 1 2 1.4 2 2V14C2 14.6 2.4 15 3 15H13C13.6 15 14 14.6 14 14V6L9 1Z"
                          fill="currentColor" />
                        <path d="M9 1V6H14" stroke="currentColor" stroke-width="1" fill="none" />
                      </svg>
                    </div>
                    <div class="file-details">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    </div>
                  </div>
                  <button class="remove-file-btn" @click="removeFile(index)" title="移除文件">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                      <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 移动端输入框 -->
            <div v-if="isMobile" class="mobile-input-container">
              <div class="input-wrapper">
                <textarea v-model="inputMessage" :placeholder="placeholder" @keydown.enter.prevent="handleEnter"
                  @input="adjustTextareaHeight" ref="textarea" rows="1" class="mobile-textarea"></textarea>

                <div class="mobile-input-buttons">
                  <button v-if="isFileUploadEnabled" class="mobile-file-btn" @click="triggerFileUpload"
                    :disabled="isLoading || isUploading || !canUploadMoreFiles">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2l-5.5 5.5h3.5v7h4v-7h3.5L12 2z" fill="currentColor" />
                      <path d="M19 15H5v2h14v-2z" fill="currentColor" />
                    </svg>
                  </button>

                  <button class="mobile-send-btn" :class="{ 'stop-mode': isLoading }"
                    @click="isLoading ? stopGeneration() : sendMessage" :disabled="!isLoading && (!inputMessage.trim())">
                    <svg v-if="!isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" fill="currentColor" />
                    </svg>
                    <div v-else class="mobile-stop-content">
                      <span>停止</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <!-- 桌面端输入框 -->
            <div v-else>
              <div class="input-container">
                <textarea v-model="inputMessage" :placeholder="placeholder" @keydown.enter.prevent="handleEnter"
                  @input="adjustTextareaHeight" ref="textarea" rows="1" ></textarea>
              </div>
              <div class="input-container" style="margin-top: 6px;">
                <button v-if="isFileUploadEnabled" class="file-upload-btn" @click="triggerFileUpload"
                  :disabled="isLoading || isUploading || !canUploadMoreFiles"
                  :title="canUploadMoreFiles ? '上传文件' : `最多只能上传${fileUploadLimits.maxFiles}个文件`">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M8 1L3 6H6V11H10V6H13L8 1Z" fill="currentColor" />
                    <path d="M2 13H14V15H2V13Z" fill="currentColor" />
                  </svg>
                </button>
                <button class="send-btn" :class="{ 'stop-mode': isLoading }"
                  @click="isLoading ? stopGeneration() : sendMessage" :disabled="!isLoading && (!inputMessage.trim())">
                  <!-- 正常发送图标 -->
                  <svg v-if="!isLoading" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M15 1L1 8L4 9L6 15L8 8L15 1Z" fill="currentColor" />
                  </svg>
                  <!-- 停止状态 -->
                  <template v-else>
                    <div class="stop-content">
                      <div class="loading-dot"></div>
                      <span class="stop-text">停止</span>
                    </div>
                  </template>
                </button>
              </div>
            </div>

            <!-- 隐藏的文件输入框 -->
            <input ref="fileInput" type="file" multiple style="display: none" @change="handleFileSelect"
              :accept="getAcceptedFileTypes()">

            <!-- 上传进度提示 -->
            <div v-if="isUploading" class="upload-progress">
              <div class="loading-spinner"></div>
              <span>正在上传文件...</span>
            </div>
          </div>

          <!-- 思考模式切换按钮 -->
          <div class="thinking-mode-toggle" :class="{ 'mobile': isMobile }">
            <button class="thinking-toggle-btn" @click="toggleThinkingMode" :class="{ 'active': showThinking }">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                <path d="M12 17a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm1-5h-2V7h2v5z" fill="currentColor"/>
              </svg>
              <span>{{ showThinking ? '隐藏思考过程' : '显示思考过程' }}</span>
            </button>
          </div>

        </div>
      </div>
    </div>

    <!-- 遮罩层 (仅弹窗模式显示) -->
    <div v-if="displayMode === 'popup' && isOpen && (showOverlay || isExpanded || isMobile)" class="chat-overlay"
      :class="{ 'expanded-overlay': isExpanded || isMobile }"
      @click="isMobile ? toggleChat : (isExpanded ? toggleExpand : toggleChat)"></div>
  </div>
</template>

<script>
import DifyApiService from '../services/difyApi.js'
import echarts from 'echarts'
import aiImg from '../assets/imgs/ai.png'
import MarkdownIt from 'markdown-it'
// import markdownItMermaid from 'markdown-it-mermaid';
// import mermaidItMarkdown from 'mermaid-it-markdown';

export default {
  name: 'DifyChat',
  props: {
    // Dify API配置
    apiUrl: {
      type: String,
      required: true
    },
    apiKey: {
      type: String,
      required: true
    },
    // 用户标识
    user: {
      type: String,
      default: 'anonymous'
    },
    // 职位
    postName: {
      type: String,
      default: '',
      required: true
    },
    // 界面配置
    title: {
      type: String,
      default: 'AI-兴仔'
    },
    placeholder: {
      type: String,
      default: '请输入您的问题...'
    },
    avatarUrl: {
      type: String,
      default: ''
    },
    // 样式配置
    position: {
      type: String,
      default: 'bottom-right', // bottom-right, bottom-left, top-right, top-left
      validator: value => ['bottom-right', 'bottom-left', 'top-right', 'top-left'].includes(value)
    },
    bubbleColor: {
      type: String,
      default: '#007bff'
    },
    showOverlay: {
      type: Boolean,
      default: false
    },
    // 功能配置
    autoOpen: {
      type: Boolean,
      default: false
    },
    maxMessages: {
      type: Number,
      default: 100
    },
    // 显示模式配置
    displayMode: {
      type: String,
      default: 'page', // popup: 弹窗模式, page: 页面模式
      validator: value => ['popup', 'page'].includes(value)
    }
  },
  data() {
    return {
      isOpen: false,
      isExpanded: false, // 是否放大显示
      messages: [],
      inputMessage: '',
      isLoading: false,
      isPaused: false, // 是否暂停状态
      conversationId: null,
      difyApi: null,
      error: null,
      currentStreamingMessage: null, // 当前正在流式接收的消息
      streamingMessageIndex: -1, // 流式消息在messages数组中的索引
      currentTaskId: null, // 当前流式任务的task_id，用于停止生成
      appParameters: {
        suggested_questions: [] // 初始化推荐问题列表
      },
      isLoadingMore: false, // 添加加载更多状态
      hasMoreMessage: true,
      echartsInstances: [], // 存储 echarts 实例
      windowWidth: window.innerWidth, // 窗口宽度
      showHistoryList: true, // 是否显示历史对话列表
      showMobileMenu: false, // 移动端菜单显示状态
      showThinking: false, // 是否显示思考过程
      // 模拟历史对话数据
      historyConversations: [],
      isLoadingHistory: false,
      isLoadingMoreHistory: false, // 加载更多历史对话状态
      lastHistoryId: null,
      hasMoreHistory: false,
      // 文件上传相关
      uploadedFiles: [], // 已上传的文件列表
      isUploading: false, // 是否正在上传文件
      // 滚动状态跟踪
      isUserAtBottom: true, // 用户是否在消息容器底部
      dragOver: false, // 拖拽状态
      fileTypes: {
        "document": '.txt, .md, .mdx, .markdown, .pdf, .html, .xlsx, .xls, .docx, .csv, .eml, .msg, .pptx, .ppt, .xml, .epub',
        "image": '.jpg, .jpeg, .png, .gif, .webp, .svg',
        "audio": '.mp3, .m4a, .wav, .webm, .amr, .mpga',
        "video": '.mp4, .mov, .mpeg, .mpga'
      },
      aiImg: aiImg,
      appInfo: null,
      mdIt: null
    }
  },
  computed: {
    // 检测是否为移动设备
    isMobile() {
      return this.windowWidth <= 768
    },
    // 检查是否启用文件上传
    isFileUploadEnabled() {
      return this.appParameters?.file_upload?.enabled === true
    },
    // 获取文件上传限制信息
    fileUploadLimits() {
      if (!this.isFileUploadEnabled) return null
      return {
        maxFiles: this.appParameters?.file_upload?.number_limits || 3,
        maxSize: this.appParameters?.file_upload?.file_size_limit || 15 * 1024 * 1024,
        allowedTypes: this.appParameters?.file_upload?.allowed_file_types || []
      }
    },
    // 检查是否可以继续上传文件
    canUploadMoreFiles() {
      if (!this.isFileUploadEnabled) return false
      const maxFiles = this.fileUploadLimits.maxFiles
      return this.uploadedFiles.length < maxFiles
    },
    bubbleStyle() {
      const positions = {
        'bottom-right': { bottom: '20px', right: '20px' },
        'bottom-left': { bottom: '20px', left: '20px' },
        'top-right': { top: '20px', right: '20px' },
        'top-left': { top: '20px', left: '20px' }
      }
      return {
        ...positions[this.position],
        backgroundColor: this.bubbleColor
      }
    },
    popupStyle() {
      // 移动端自动全屏显示
      if (this.isMobile) {
        return {
          position: 'fixed',
          top: '0',
          left: '0',
          width: '100vw',
          height: '100vh',
          maxWidth: 'none',
          maxHeight: 'none',
          borderRadius: '0'
        }
      } else if (this.isExpanded) {
        // 桌面端放大模式：居中显示，占据大部分屏幕
        const width = this.showHistoryList ? '95vw' : '90vw'
        const maxWidth = this.showHistoryList ? '1400px' : '1200px'
        return {
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: width,
          height: '90vh',
          maxWidth: maxWidth,
          maxHeight: '800px'
        }
      } else {
        // 桌面端正常模式：根据position属性定位
        const width = this.showHistoryList ? '750px' : '450px'
        const positions = {
          'bottom-right': { bottom: '80px', right: '20px', width: width },
          'bottom-left': { bottom: '80px', left: '20px', width: width },
          'top-right': { top: '80px', right: '20px', width: width },
          'top-left': { top: '80px', left: '20px', width: width }
        }
        return positions[this.position]
      }
    },
    pageStyle() {
      // 页面模式样式：占满整个容器
      return {
        position: 'relative',
        width: '100%',
        height: '100%',
        maxWidth: 'none',
        maxHeight: 'none',
        borderRadius: '0',
        border: 'none',
        boxShadow: 'none'
      }
    },
    // 将对话按时间分组
    groupedConversations() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);

      // 创建分组
      const groups = [
        { label: '今天', conversations: [] },
        { label: '最近七天', conversations: [] },
        { label: '更早', conversations: [] }
      ];

      // 对对话进行分组
      this.historyConversations.forEach(conversation => {
        const conversationDate = new Date(
          conversation.timestamp.getFullYear(),
          conversation.timestamp.getMonth(),
          conversation.timestamp.getDate()
        );

        if (conversationDate.getTime() === today.getTime()) {
          // 今天的对话
          groups[0].conversations.push(conversation);
        } else if (conversationDate >= weekAgo) {
          // 最近七天的对话
          groups[1].conversations.push(conversation);
        } else {
          // 更早的对话
          groups[2].conversations.push(conversation);
        }
      });

      // 只返回有对话的分组
      return groups;
    },
    currentConversationTitle() {
      return this.historyConversations.find(c => c.id === this.conversationId)?.title || ''
    }
  },
  mounted() {
    this.mdIt = new MarkdownIt({
      html: true, // 启用HTML标签
      breaks: true, // 转换换行符为<br>
      linkify: true, // 自动转换URL为链接
      typographer: true // 启用语言中性的替换
    })
    this.mdIt
      .use(function (md) {
        md.renderer.rules.fence = function (tokens, idx, options, env, slf) {
          // 此处判断是否为 echarts 代码块
          if (tokens[idx].info === "echarts") {

            // const chartId = 'echarts-' + Math.random().toString(36).substr(2, 9);
            const sanitizedConfig = tokens[idx].content
              .replace(/<script/gi, '&lt;script')
              .replace(/<\/script/gi, '&lt;/script');
            // const eChartOption = tokens[idx].content //此处表示将内容存起来，存到当前页面的变量去
            return `<div class="echarts-container" data-config="${encodeURIComponent(sanitizedConfig)}" style="width:100%;height:400px;">
            <div class="chart-loading-container">
              <div class="chart-loading-text">图表加载中</div>
            </div>
          </div>`
          } else { // 其他代码块
            const language = tokens[idx].info || 'text';
            const languageTitle = language.charAt(0).toUpperCase() + language.slice(1);
            return `<div class="code-block-container">
            <div class="code-block-header">
              <span class="code-language-title">${languageTitle}</span>
            </div>
            <pre><code class='language-${language}'>${tokens[idx].content}</code></pre>
          </div>`;
          }
        }
      })
      // .use(mermaidItMarkdown)
    // 初始化Dify API服务
    if (this.apiUrl && this.apiKey) {
      this.difyApi = new DifyApiService(this.apiUrl, this.apiKey)
      this.getAppInfo(); // 获取应用信息
      this.getAppParameters(); // 获取应用参数
      this.loadHistoryConversations(); // 加载历史对话列表
    }

    if(this.isMobile){
      this.showHistoryList = false
    }

    if (this.autoOpen || this.displayMode === 'page') {
      this.isOpen = true
    }

    // 监听窗口大小变化
    this.handleResize = () => {
      this.windowWidth = window.innerWidth
    }
    window.addEventListener('resize', this.handleResize)

    // 初始化滚动状态
    this.$nextTick(() => {
      this.isUserAtBottom = true
    })
  },

  beforeDestroy() {
    // 清理事件监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize)
    }
    // 清理echarts实例
    if (this.echartsInstances && this.echartsInstances.length > 0) {
      this.echartsInstances.forEach(chart => {
        if (chart && chart.dispose) {
          chart.dispose()
        }
      })
    }
  },

  watch: {
    // messages(newMessages, oldMessages){
    //   if (newMessages.length&&this.$refs['markdownBody']) {
    //     console.log(newMessages);
    //     newMessages.forEach((item,index)=>{
    //       console.log(item,this.$refs['markdownBody']);

    //       this.$refs['markdownBody'][index].innerHTML = item.content;
    //     })
    //   }
    // }
  },
  methods: {
    // 获取应用信息
    async getAppInfo() {
      try {
        const response = await this.difyApi.getAppInfo()
        this.appInfo = response
      } catch (error) {
        console.error('获取应用信息失败:', error);
      }
    },
    // 获取应用参数
    async getAppParameters() {
      try {
        // 使用POST请求替代GET请求
        const response = await fetch(this.apiUrl + '/parameters', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },

        });
        const data = await response.json();
        this.appParameters = data;
        if (this.appParameters.opening_statement) {
          this.addMessage('assistant', this.appParameters.opening_statement, [], true) // 开场白强制滚动
        }
      } catch (error) {
        console.error('获取应用参数失败:', error);
      }
    },
    toggleChat() {
      this.isOpen = !this.isOpen
      if (this.isOpen) {
        this.$nextTick(() => {
          this.forceScrollToBottom() // 用户主动打开聊天，强制滚动到底部
          if (this.$refs.textarea) {
            this.$refs.textarea.focus()
          }
        })
        this.initEchartsInstances()
      } else {
        // 关闭聊天时也重置放大状态
        this.isExpanded = false
      }
    },

    toggleExpand() {
      this.isExpanded = !this.isExpanded
      this.$nextTick(() => {
        this.forceScrollToBottom() // 用户主动切换展开状态，强制滚动到底部
        if (this.$refs.textarea) {
          this.$refs.textarea.focus()
        }
        setTimeout(() => {
          this.resizeAllCharts()
        }, 300);
      })
    },

    // 调整所有图表大小
    resizeAllCharts() {
      if (this.echartsInstances && this.echartsInstances.length > 0) {
        this.$nextTick(() => {
          this.echartsInstances.forEach(chart => {
            if (chart && chart.resize) {
              chart.resize();
            }
          });
        });
      }
    },

    // 切换移动端菜单显示
    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    },

    // 切换思考模式显示
    toggleThinkingMode() {
      this.showThinking = !this.showThinking
      // 更新所有消息的思考过程显示状态
      this.messages.forEach(message => {
        if (message.thinking) {
          message.showThinking = this.showThinking
        }
      })
    },

    // 切换历史对话列表显示
    toggleHistoryList() {
      this.showHistoryList = !this.showHistoryList

      this.$nextTick(() => {
        this.forceScrollToBottom() // 用户主动切换历史列表，强制滚动到底部
        setTimeout(() => {
          this.resizeAllCharts()
        }, 300);
      })
    },

    // 加载历史对话列表
    async loadHistoryConversations() {
      if (!this.difyApi || this.isLoadingHistory) return

      try {
        this.isLoadingHistory = true
        const response = await this.difyApi.getConversationHistoryList(this.user, 20, null)
        this.hasMoreHistory = response.has_more
        if (response && response.data && Array.isArray(response.data)) {
          this.historyConversations = response.data.map(conversation => ({
            id: conversation.id,
            title: conversation.name || '未命名对话',
            lastMessage: this.getLastMessagePreview(conversation),
            timestamp: new Date(conversation.updated_at * 1000),
            messageCount: conversation.message_count || 0,
            originalData: conversation // 保存原始数据
          }))
          if (this.historyConversations.length > 0) {
            this.lastHistoryId = this.historyConversations[this.historyConversations.length - 1].id
            // this.conversationId = this.historyConversations[0].id
            // this.loadConversationHistory()
          }
        }

      } catch (error) {
        console.error('加载历史对话失败:', error)
      } finally {
        this.isLoadingHistory = false
      }
    },

    // 加载更多历史对话
    async loadMoreHistoryConversations() {
      if (!this.difyApi || this.isLoadingMoreHistory || !this.hasMoreHistory) return

      try {
        this.isLoadingMoreHistory = true
        const response = await this.difyApi.getConversationHistoryList(this.user, 10, this.lastHistoryId)
        this.hasMoreHistory = response.has_more

        if (response && response.data && Array.isArray(response.data)) {
          const newConversations = response.data.map(conversation => ({
            id: conversation.id,
            title: conversation.name || '未命名对话',
            lastMessage: this.getLastMessagePreview(conversation),
            timestamp: new Date(conversation.updated_at * 1000),
            messageCount: conversation.message_count || 0,
            originalData: conversation // 保存原始数据
          }))

          // 追加新的对话到现有列表
          this.historyConversations = [...this.historyConversations, ...newConversations]

          if (newConversations.length > 0) {
            this.lastHistoryId = newConversations[newConversations.length - 1].id
          }
        }

      } catch (error) {
        console.error('加载更多历史对话失败:', error)
      } finally {
        this.isLoadingMoreHistory = false
      }
    },

    // 历史对话列表滚动监听
    handleHistoryScroll(event) {
      const container = event.target
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      // 当滚动到底部附近时（距离底部小于50px）触发加载更多
      if (scrollHeight - scrollTop - clientHeight < 50 && this.hasMoreHistory && !this.isLoadingMoreHistory) {
        this.loadMoreHistoryConversations()
      }
    },

    // 获取对话的最后消息预览
    getLastMessagePreview(conversation) {
      // 如果API返回了最后消息，使用它
      if (conversation.last_message) {
        return conversation.last_message.length > 50
          ? conversation.last_message.substring(0, 50) + '...'
          : conversation.last_message
      }

      // 否则返回默认文本
      return '点击查看对话内容'
    },

    // 加载指定对话
    loadConversation(conversationId) {
      if (this.conversationId === conversationId) return

      this.conversationId = conversationId
      this.messages = []
      this.isLoading = true
      // 重置滚动状态
      this.isUserAtBottom = true

      // 加载对话历史
      this.loadConversationHistory()
      if (this.isMobile) {
        this.toggleHistoryList()
      }
    },

    // 开始新对话
    startNewConversation() {
      this.conversationId = null
      this.messages = []
      // 重置滚动状态
      this.isUserAtBottom = true
      if (this.appParameters.opening_statement) {
        this.addMessage('assistant', this.appParameters.opening_statement, [], true) // 新对话开场白强制滚动
      }
      if (this.isMobile) {
        this.toggleHistoryList()
      }
    },

    // 删除对话
    deleteConversation(conversationId) {
      if (confirm('确定要删除这个对话吗？')) {
        this.historyConversations = this.historyConversations.filter(c => c.id !== conversationId)
        this.difyApi.deleteConversations(conversationId, this.user);
        if (this.conversationId === conversationId) {
          this.startNewConversation()
        }
      }
    },

    // 格式化历史时间
    formatHistoryTime(timestamp) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const messageDate = new Date(timestamp.getFullYear(), timestamp.getMonth(), timestamp.getDate());
      const diffDays = Math.floor((today - messageDate) / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        // 今天：只显示时间
        return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (diffDays < 7) {
        // 一周内：显示星期几
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return weekdays[timestamp.getDay()];
      } else if (timestamp.getFullYear() === now.getFullYear()) {
        // 今年：显示月-日
        return timestamp.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' });
      } else {
        // 往年：显示年-月
        return timestamp.toLocaleDateString('zh-CN', { year: 'numeric', month: 'numeric' });
      }
    },

    async sendMessage() {
      if ((!this.inputMessage.trim()) || this.isLoading) return

      const userMessage = this.inputMessage.trim()
      const files = [...this.uploadedFiles] // 复制文件列表

      this.inputMessage = ''
      this.uploadedFiles = [] // 清空已上传文件
      this.adjustTextareaHeight()

      // 添加用户消息（包含文件信息）
      this.addMessage('user', userMessage, files)

      // 发送到Dify API
      await this.sendToDify(userMessage, files)
    },

    handleEnter(event) {
      if (!event.shiftKey) {
        this.sendMessage()
      }
    },

    // 停止生成
    async stopGeneration() {
      console.log('停止生成')

      // 如果有task_id，调用停止接口
      if (this.currentTaskId && this.difyApi) {
        try {
          await this.difyApi.stopGeneration(this.currentTaskId, this.user)
          console.log('停止生成请求已发送')
        } catch (error) {
          console.error('停止生成失败:', error)
        }
      }

      // 设置暂停状态
      this.isPaused = true
      this.isLoading = false

      // 如果有正在流式接收的消息，标记为暂停
      if (this.currentStreamingMessage) {
        this.currentStreamingMessage.isPaused = true
        this.currentStreamingMessage.isStreaming = false
      }

      this.currentStreamingMessage = null
      this.streamingMessageIndex = -1
      this.currentTaskId = null // 清空task_id
    },

    adjustTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.textarea
        if (textarea) {
          textarea.style.height = 'auto'

          textarea.style.height = Math.min(textarea.scrollHeight - 24, 120) + 'px'
        }
      })
    },

    addMessage(type, content, files = [], forceScroll = false) {
      const message = {
        type,
        content,
        files: files || [],
        timestamp: new Date()
      }
      this.messages.push(message)

      // 限制消息数量
      if (this.messages.length > this.maxMessages) {
        this.messages = this.messages.slice(-this.maxMessages)
      }

      this.$nextTick(() => {
        // 用户消息或强制滚动时，强制滚动到底部；否则智能滚动
        if (type === 'user' || forceScroll) {
          this.forceScrollToBottom()
        } else {
          this.scrollToBottom()
        }
      })

    },

    // 检查用户是否在消息容器底部
    checkIfUserAtBottom(container) {
      if (!container) return true
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight
      // 距离底部小于50px认为是在底部
      return scrollHeight - scrollTop - clientHeight < 50
    },

    // 滚动到底部（只在用户在底部时才自动滚动）
    scrollToBottom(force = false) {
      const container = this.$refs.messagesContainer
      if (container) {
        // 如果强制滚动或用户在底部，则滚动到底部
        if (force || this.isUserAtBottom) {
          container.scrollTop = container.scrollHeight
          this.isUserAtBottom = true
        }
      }
    },

    // 强制滚动到底部（用于用户主动操作）
    forceScrollToBottom() {
      this.scrollToBottom(true)
    },

    formatMessage(content) {
      if (!content) return '';
      // 处理 Echarts 图表配置
      return this.mdIt.render(content)
    },
    initEchartsInstances() {
      this.$nextTick(() => {
        // 查找所有echarts容器
        const containers = document.querySelectorAll('.echarts-container');
        containers.forEach(container => {
          try {
            // 获取并解码配置字符串
            const encodedConfig = container.getAttribute('data-config');
            const configStr = decodeURIComponent(encodedConfig);

            // 尝试解析配置
            let config;

            try {
              // 首先尝试作为JSON解析
              config = JSON.parse(configStr);
            } catch (e) {
              // 如果JSON解析失败，尝试作为JavaScript对象字面量解析
              try {
                // 使用Function构造函数，但添加安全检查
                if (configStr.includes('document.') ||
                  configStr.includes('window.') ||
                  configStr.includes('eval(') ||
                  configStr.includes('setTimeout(') ||
                  configStr.includes('setInterval(')) {
                  throw new Error('配置包含潜在的不安全代码');
                }

                config = new Function('return ' + configStr)();
              } catch (evalError) {
                console.error('无法解析Echarts配置:', evalError);
                throw evalError;
              }
            }

            // 初始化图表
            const chart = echarts.init(container);
            chart.setOption({
              ...config,
              grid: {
                left: 20,
                right: 20,
                bottom: 20,
                top: 80,
                containLabel: true
              }
            });

            // 保存实例以便后续可能的操作
            if (!this.echartsInstances) this.echartsInstances = [];
            this.echartsInstances.push(chart);

            // 添加窗口大小变化时的自适应
            window.addEventListener('resize', () => {
              chart.resize();
            });
          } catch (e) {
            console.error('初始化Echarts图表失败:', e);
            container.innerHTML = '<div class="echarts-error">图表加载失败</div>';
          }
        });
      });
    },
    formatTime(timestamp) {
      if (timestamp) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const messageDate = new Date(timestamp.getFullYear(), timestamp.getMonth(), timestamp.getDate());
        const diffDays = Math.floor((today - messageDate) / (1000 * 60 * 60 * 24));
        if (diffDays === 0) {
          // 今天：只显示时间
          return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
        return timestamp.toLocaleTimeString('zh-CN', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return ''
    },

    async sendToDify(message, files = []) {
      if (!this.difyApi) {
        this.addMessage('assistant', '抱歉，请先配置正确的API URL和API Key')
        return
      }

      this.isLoading = true
      this.isPaused = false // 重置暂停状态
      this.error = null
      // 使用流式处理
      await this.sendWithStreaming(message, files)

    },

    // 流式处理发送消息
    async sendWithStreaming(message, files = []) {
      this.currentStreamingMessage = null;
      this.streamingMessageIndex = -1;
      this.currentTaskId = null; // 重置task_id

      // 添加一个空的助手消息作为流式内容的容器
      const assistantMessage = {
        type: 'assistant',
        content: '',
        thinking: '',
        timestamp: new Date(),
        isStreaming: true,
        isComplete: false,
        showThinking: true, // 默认展开思考过程，便于实时查看
        isThinking: false // 初始状态不是思考状态，等待第一个思考内容到达
      };

      this.messages.push(assistantMessage);
      this.streamingMessageIndex = this.messages.length - 1;
      this.currentStreamingMessage = assistantMessage;

      this.$nextTick(() => {
        this.forceScrollToBottom(); // 开始流式响应时强制滚动到底部
      });

      try {
        // 准备文件数据
        const fileData = files.map(file => ({
          transfer_method: 'local_file',
          type: this.findFileType(file.extension),
          upload_file_id: file.id,
          url: ''
        }));

        await this.difyApi.sendMessageStream(
          message,
          this.user,
          this.conversationId,
          this.inputs || { post_name: this.postName },
          fileData,
          // onMessage 回调 - 处理流式消息
          (streamData) => {
            this.handleStreamMessage(streamData);
          },
          // onComplete 回调 - 流式完成
          (response) => {
            this.handleStreamComplete(response);
          },
          // onError 回调 - 流式错误
          (error) => {
            this.handleStreamError(error);
          }
        );

      } catch (error) {
        console.error('发送流式消息失败:', error);
        this.handleStreamError(error);
      }
    },

    // 处理流式消息
    handleStreamMessage(streamData) {
      if (!this.currentStreamingMessage) return;

      // 保存task_id用于停止生成
      if (streamData.task_id && !this.currentTaskId) {
        this.currentTaskId = streamData.task_id;
      }

      // 创建一个新对象而不是修改原对象
      const updatedMessage = { ...this.currentStreamingMessage };

      if (streamData.type === 'message_update') {
        // 更新思考过程和内容
        updatedMessage.thinking = streamData.thinking || '';
        updatedMessage.isComplete = streamData.isComplete;
        updatedMessage.isThinking = streamData.isThinking;

        // 更新内容
        updatedMessage.content = this.formatMessage(streamData.content);

        // 如果有内容或思考过程，标记为正在流式传输
        if (streamData.content || streamData.thinking) {
          updatedMessage.isStreaming = true;
        }
      }

      // 替换整个对象以确保Vue检测到变化
      this.$set(this.messages, this.streamingMessageIndex, updatedMessage);
      this.currentStreamingMessage = updatedMessage;

      // this.$refs['markdownBody'].forEach((item, index) => {
      //   if (index === this.streamingMessageIndex) {
      //     this.$refs['markdownBody'][index].innerHTML = updatedMessage.content;
      //   }
      // })
      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 切换思考过程显示
    toggleThinking(messageIndex) {
      if (this.messages[messageIndex]) {
        this.$set(this.messages[messageIndex], 'showThinking', !this.messages[messageIndex].showThinking);
      }
    },

    // 处理流式完成
    handleStreamComplete(response) {
      if (this.currentStreamingMessage) {
        // 标记消息为完成状态
        this.currentStreamingMessage.isStreaming = false;
        this.currentStreamingMessage.isComplete = true;
        this.currentStreamingMessage.isThinking = false;
        this.currentStreamingMessage.content = this.formatMessage(response.answer) || this.currentStreamingMessage.content;

        // 更新思考内容
        if (response.thinking) {
          this.currentStreamingMessage.thinking = response.thinking;
        }

        // 更新消息数组
        if (this.streamingMessageIndex >= 0) {
          this.$set(this.messages, this.streamingMessageIndex, {
            ...this.currentStreamingMessage
          });
        }
      }

      if (!this.conversationId) {
        this.loadHistoryConversations()
      }
      // 更新会话ID
      if (response.conversation_id) {
        this.conversationId = response.conversation_id
      }

      // 限制消息数量
      if (this.messages.length > this.maxMessages) {
        this.messages = this.messages.slice(-this.maxMessages)
      }

      // 重置状态
      this.isLoading = false;
      this.currentStreamingMessage = null;
      this.streamingMessageIndex = -1;
      this.currentTaskId = null; // 清空task_id
      this.initEchartsInstances()
    },

    // 处理流式错误
    handleStreamError(error) {
      console.error('Dify API Stream Error:', error)
      this.error = error

      // 移除未完成的流式消息
      if (this.streamingMessageIndex >= 0) {
        this.messages.splice(this.streamingMessageIndex, 1)
      }

      let errorMessage = '抱歉，发生了错误，请稍后重试。'

      if (error.type === 'network_error') {
        errorMessage = '网络连接失败，请检查网络设置后重试。'
      } else if (error.type === 'api_error') {
        if (error.status === 401) {
          errorMessage = 'API密钥无效，请检查配置。'
        } else if (error.status === 429) {
          errorMessage = '请求过于频繁，请稍后重试。'
        } else {
          errorMessage = error.message || errorMessage
        }
      }

      this.addMessage('assistant', errorMessage)

      // 清理流式状态
      this.currentStreamingMessage = null
      this.streamingMessageIndex = -1
      this.isLoading = false

    },


    // 清空会话
    clearConversation() {
      this.messages = []
      this.conversationId = null
      this.addMessage('assistant', '会话已清空，有什么可以帮助您的吗？', [], true) // 清空会话消息强制滚动
    },

    // 加载历史对话消息
    async loadConversationHistory() {
      if (!this.difyApi || !this.conversationId) return

      try {
        this.isLoading = true
        const history = await this.difyApi.getConversationHistory(this.user, this.conversationId)

        if (history && history.data && Array.isArray(history.data)) {
          // 清空现有消息
          this.messages = []

          // 处理历史消息数据
          history.data.forEach(msg => {
            // 添加用户消息
            if (msg.query && msg.query.trim()) {
              this.messages.push({
                type: 'user',
                content: msg.query,
                timestamp: new Date(msg.created_at * 1000), // 转换时间戳
                id: msg.id,
                files: msg.message_files.map(item => {
                  return {
                    id: item.id,
                    name: item.filename,
                    size: item.size,
                    mime_type: item.mime_type,
                  }
                })
              })
            }

            // 添加助手回复消息
            if (msg.answer && msg.answer.trim()) {
              // 提取思考内容和正文内容
              const thinkingResult = this.extractThinkingFromAnswer(msg.answer)

              this.messages.push({
                type: 'assistant',
                content: this.formatMessage(thinkingResult.cleanedText),
                timestamp: new Date(msg.created_at * 1000), // 转换时间戳
                thinking: thinkingResult.thinking,
                showThinking: false,
                id: msg.id,
                feedback: msg.feedback,
                status: msg.status
              })
            }
          })

          // 按时间排序（确保消息顺序正确）
          this.messages.sort((a, b) => a.timestamp - b.timestamp)

          this.$nextTick(() => {
            this.forceScrollToBottom() // 加载历史消息后强制滚动到底部
          })
          this.initEchartsInstances()
        }

        // 如果无分页对话且有开场白
        this.hasMoreMessage = history.has_more
        if (!history.has_more && this.appParameters.opening_statement) {
          this.messages.unshift({
            type: 'assistant',
            content: this.appParameters.opening_statement,
            timestamp: ''
          })
        }
      } catch (error) {
        console.error('加载历史消息失败:', error)
        this.error = error
      } finally {
        this.isLoading = false
      }
    },
    handleScroll(e) {
      const container = e.target;

      // 当滚动到顶部时触发
      if (container.scrollTop === 0 && !this.isLoadingMore && this.hasMoreMessage) {
        this.loadMoreMessages(container.scrollHeight);
      }

      // 跟踪用户是否在底部附近（距离底部小于50px认为是在底部）
      this.isUserAtBottom = this.checkIfUserAtBottom(container);
    },
    async loadMoreMessages(containerHeight) {
      if (this.isLoadingMore) return;

      this.isLoadingMore = true;
      try {
        // 这里可以添加加载更多历史消息的逻辑
        // 例如：调用API获取更早的消息
        await this.fetchMoreMessages(containerHeight);
      } catch (error) {
        console.error('加载更多消息失败:', error);
        this.$Message.error('加载更多消息失败');
      } finally {
        this.isLoadingMore = false;
      }
    },
    async fetchMoreMessages(containerHeight) {
      // 这里实现获取更多历史消息的具体逻辑
      // 例如：根据当前最早消息的时间戳获取更早的消息
      // 示例代码：
      const earliestMessage = this.messages[0];

      if (!earliestMessage.id) {
        return
      }
      const history = await this.difyApi.getConversationHistory(this.user, this.conversationId, earliestMessage.id)
      let moreMessage = []
      if (history && history.data && Array.isArray(history.data)) {
        // 处理历史消息数据
        history.data.forEach(msg => {
          // 添加用户消息
          if (msg.query && msg.query.trim()) {
            moreMessage.push({
              type: 'user',
              content: msg.query,
              timestamp: new Date(msg.created_at * 1000), // 转换时间戳
              id: msg.id,
              files: msg.message_files.map(item => {
                return {
                  id: item.id,
                  name: item.filename,
                  size: item.size,
                  mime_type: item.mime_type,
                }
              })
            })
          }

          // 添加助手回复消息
          if (msg.answer && msg.answer.trim()) {
            // 提取思考内容和正文内容
            const thinkingResult = this.extractThinkingFromAnswer(msg.answer)

            moreMessage.push({
              type: 'assistant',
              content: this.formatMessage(thinkingResult.cleanedText),
              timestamp: new Date(msg.created_at * 1000), // 转换时间戳
              thinking: thinkingResult.thinking,
              showThinking: false,
              id: msg.id,
              feedback: msg.feedback,
              status: msg.status
            })
          }
        })

        this.messages = [...moreMessage, ...this.messages]
        // 按时间排序（确保消息顺序正确）
        this.messages.sort((a, b) => a.timestamp - b.timestamp)
        // 如果无分页对话且有开场白
        this.hasMoreMessage = history.has_more
        if (!history.has_more && this.appParameters.opening_statement) {
          this.messages.unshift({
            type: 'assistant',
            content: this.appParameters.opening_statement,
            timestamp: ''
          })
        }
        this.$nextTick(() => {
          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight - containerHeight
        })
        this.initEchartsInstances()
      }
    },
    // 从回答中提取思考内容
    extractThinkingFromAnswer(answer) {
      if (!answer) return { thinking: '', cleanedText: '' }

      // 检测 <think> 标签
      const thinkRegex = /<think>([\s\S]*?)<\/think>/g
      let thinking = ''
      let cleanedText = answer

      // 提取所有思考内容
      let match
      while ((match = thinkRegex.exec(answer)) !== null) {
        thinking += match[1].trim() + '\n\n'
      }

      // 移除思考标签，保留正文
      cleanedText = answer.replace(thinkRegex, '').trim()

      return {
        thinking: thinking.trim(),
        cleanedText: cleanedText
      }
    },

    // 文件上传相关方法
    triggerFileUpload() {
      this.$refs.fileInput.click()
    },

    async handleFileSelect(event) {
      const files = Array.from(event.target.files)
      if (files.length > 0) {
        await this.uploadFiles(files)
      }
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    },

    async uploadFiles(files) {
      if (!this.difyApi) {
        return
      }

      // 检查文件数量限制
      if (!this.fileUploadLimits) return

      const maxFiles = this.fileUploadLimits.maxFiles
      const currentFileCount = this.uploadedFiles.length
      const newFileCount = files.length

      if (currentFileCount + newFileCount > maxFiles) {
        const allowedCount = maxFiles - currentFileCount
        if (allowedCount <= 0) {
          alert(`最多只能上传 ${maxFiles} 个文件，请先删除一些文件后再上传`)
          return
        } else {
          alert(`最多只能上传 ${maxFiles} 个文件，当前已有 ${currentFileCount} 个，只能再上传 ${allowedCount} 个文件`)
          // 只处理允许的文件数量
          files = files.slice(0, allowedCount)
        }
      }

      this.isUploading = true
      const uploadPromises = files.map(file => this.uploadSingleFile(file))

      try {
        const results = await Promise.all(uploadPromises)
        const successfulUploads = results.filter(result => result.success)

        // 添加成功上传的文件到列表
        this.uploadedFiles.push(...successfulUploads.map(result => result.file))
      } catch (error) {
        console.error('文件上传失败:', error)
      } finally {
        this.isUploading = false
      }
    },

    async uploadSingleFile(file) {
      try {
        // 检查文件大小和类型
        if (!this.validateFile(file)) {
          return { success: false, error: '文件验证失败' }
        }

        const response = await this.difyApi.uploadFile(file, this.user)

        return {
          success: true,
          file: {
            id: response.id,
            name: response.name,
            size: response.size,
            extension: response.extension,
            mime_type: response.mime_type,
            originalFile: file
          }
        }
      } catch (error) {
        console.error('单个文件上传失败:', error)
        return { success: false, error: error.message }
      }
    },

    validateFile(file) {
      if (!this.fileUploadLimits) return false

      const { maxSize } = this.fileUploadLimits

      if (file.size > maxSize) {
        alert(`文件大小不能超过 ${this.formatFileSize(maxSize)}`)
        return false
      }
      const allowedTypes = this.getAcceptedFileTypes().split(',')
      if (allowedTypes.length > 0) {
        const fileExtension = file.name.split('.').pop().toLowerCase()
        if (!allowedTypes.some(type => type.indexOf(fileExtension) !== -1)) {
          alert(`不支持的文件类型: ${fileExtension}。支持的类型: ${allowedTypes.join(', ')}`)
          return false
        }
      }

      return true
    },

    removeFile(index) {
      this.uploadedFiles.splice(index, 1)
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    getAcceptedFileTypes() {
      if (!this.appParameters.file_upload) return ''
      if (this.appParameters.file_upload && this.appParameters.file_upload.allowed_file_types.length === 1 && this.appParameters.file_upload.allowed_file_types[0] === 'custom') {
        return this.appParameters.file_upload.allowed_file_extensions;
      }
      return this.appParameters.file_upload.allowed_file_types.map(type => this.fileTypes[type]).join(',');
    },

    // 拖拽上传相关方法
    handleDragOver(event) {
      if (!this.isFileUploadEnabled) return
      event.preventDefault()
      this.dragOver = true
    },

    handleDragLeave(event) {
      if (!this.isFileUploadEnabled) return
      event.preventDefault()
      this.dragOver = false
    },

    async handleDrop(event) {
      if (!this.isFileUploadEnabled) return
      event.preventDefault()
      this.dragOver = false

      const files = Array.from(event.dataTransfer.files)
      if (files.length > 0) {
        await this.uploadFiles(files)
      }
    },
    findFileType(extension) {
      if (!extension) return 'document';

      // 确保扩展名格式一致（小写，带点）
      const ext = extension.toLowerCase().startsWith('.') ? extension.toLowerCase() : '.' + extension.toLowerCase();

      // 检查每种文件类型
      if (this.fileTypes.document.split(',').some(type => type.trim().toLowerCase() === ext)) {
        return 'document';
      }
      if (this.fileTypes.image.split(',').some(type => type.trim().toLowerCase() === ext)) {
        return 'image';
      }
      if (this.fileTypes.audio.split(',').some(type => type.trim().toLowerCase() === ext)) {
        return 'audio';
      }
      if (this.fileTypes.video.split(',').some(type => type.trim().toLowerCase() === ext)) {
        return 'video';
      }

      // 根据常见扩展名进行额外检查
      const documentExts = ['.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.md', '.csv'];
      const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.bmp', '.tiff'];
      const audioExts = ['.mp3', '.wav', '.ogg', '.m4a', '.flac', '.aac'];
      const videoExts = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.flv'];

      if (documentExts.includes(ext)) return 'document';
      if (imageExts.includes(ext)) return 'image';
      if (audioExts.includes(ext)) return 'audio';
      if (videoExts.includes(ext)) return 'video';

      // 默认为文档类型
      return 'document';
    },
    // 处理推荐问题点击
    handleSuggestedQuestion(question) {
      this.inputMessage = question;
      this.sendMessage();
    },

    handleBack() {
      this.$emit('go-agent')
    }
  }
}
</script>

<style scoped lang="less">
.dify-chat-wrapper {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  --primary-color: #007bff;
  --primary-gradient: linear-gradient(135deg, #3998fd 0%, #0059b9 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面模式样式 */
.dify-chat-wrapper.page-mode {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: auto;
  font-size: 16px;
}

/* 聊天气泡 */
.chat-bubble {
  position: fixed;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-medium), 0 0 0 0 rgba(102, 126, 234, 0.4);
  transition: var(--transition);
  z-index: 10000;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  // animation: pulse 2s infinite;
}

.chat-bubble:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-heavy), 0 0 20px rgba(102, 126, 234, 0.6);
  animation: none;
}

@keyframes pulse {
  0% {
    box-shadow: var(--shadow-medium), 0 0 0 0 rgba(102, 126, 234, 0.4);
  }

  70% {
    box-shadow: var(--shadow-medium), 0 0 0 10px rgba(102, 126, 234, 0);
  }

  100% {
    box-shadow: var(--shadow-medium), 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* 聊天弹窗 */
.chat-popup {
  position: fixed;
  width: 450px;
  height: 700px;
  background: linear-gradient(145deg, #ffffff 0%, #c5e1fa 100%);
  // background: linear-gradient(145deg, #ffffff 0%, #f0f8ff 100%);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy), 0 0 0 1px rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10001;
  transition: var(--transition);
  backdrop-filter: blur(20px);
  // border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 页面模式的聊天弹窗 */
.chat-popup.page-mode {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0;
  box-shadow: none;
  animation: none;
  z-index: auto;
  backdrop-filter: none;
  border: none;
}

/* 主体内容区域 */
.chat-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 聊天区域 */
.chat-area {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

}

/* 放大模式样式 */
.chat-popup.expanded {
  animation: expandUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-heavy), 0 20px 60px rgba(0, 0, 0, 0.3);
  background: linear-gradient(145deg, #ffffff 0%, #f0f8ff 100%);
}



@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes expandUp {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
    filter: blur(10px);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: blur(0);
  }
}

@keyframes slideUpMobile {
  from {
    opacity: 0;
    transform: translateY(100%) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 头部 */
.chat-header {
  padding: 16px 24px;
  // background: var(--primary-gradient);

  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

/* 当前聊天标题样式 */
.current-chat-title {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  position: relative;
}

.current-chat-title span {
  position: relative;
  padding-bottom: 2px;
}

.current-chat-title span::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.current-chat-title:hover span::after {
  opacity: 1;
  background: white;
}

@media (max-width: 768px) {
  .current-chat-title {
    max-width: 150px;
    font-size: 15px;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

/* 头像样式 */
.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.chat-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 700;
  font-size: 25px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
  padding: 12px 8px 12px 12px;
}

.expand-btn,
.close-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.expand-btn:hover,
.close-btn:hover {
  // background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.expand-btn {
  margin-right: 8px;
}

/* 页面右上角返回按钮 */
.page-back-button {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
}

.page-back-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // width: 36px;
  height: 36px;
}

.page-back-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.page-back-btn svg {
  width: 16px;
  height: 16px;
}

/* 历史对话按钮 */
.history-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  backdrop-filter: blur(10px);
}

.history-btn:hover {
  // background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 历史对话侧边栏 */
.history-sidebar {
  width: 280px;
  background: #f7f8fc;
  // background: linear-gradient(165deg, #f8f9ff 0%, #eef2fa 100%);
  // background: linear-gradient(165deg,#4da2fd50 0%, #4da2fd 100%);
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  box-shadow: inset -5px 0 15px -5px rgba(0, 123, 255, 0.05);
}


/* 历史对话标题样式 */
.history-header {
  padding: 10px 16px;
  // background: rgba(248, 249, 255, 0.7);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.history-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #667788;
  text-align: center;
  letter-spacing: 0.5px;
}

.history-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 移动端返回按钮 */
.history-back-btn {
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.history-back-btn:hover {
  background-color: #f8f9fa;
}

/* 历史对话关闭按钮 */
.history-close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(180deg);
}

.history-close-btn:hover {
  color: #333;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background: var(--primary-gradient);
  color: white;
  border-radius: 8px;
  margin: 10px 15px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
  }

  button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: rotate(90deg);
    }
  }
}

/* 历史对话列表 */
.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.history-item {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 123, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 14px;
  position: relative;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.history-item:hover {
  border-color: rgba(0, 123, 255, 0.5);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.history-item.active {
  border-color: #007bff;
  background: linear-gradient(135deg, rgba(240, 248, 255, 0.9) 0%, rgba(230, 244, 255, 0.9) 100%);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.history-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background: transparent;
  border-radius: 4px 0 0 4px;
  transition: all 0.3s;
}

.history-item.active::before {
  background: var(--primary-gradient);
}

.history-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 11px;
  color: #7f8c8d;
  flex-shrink: 0;
}

.delete-conversation-btn {
  background: none;
  border: none;
  color: #95a5a6;
  cursor: pointer;
  padding: 5px;
  border-radius: 6px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  margin-left: 8px;
  flex-shrink: 0;
}

.history-item:hover .delete-conversation-btn {
  opacity: 1;
}

.delete-conversation-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  transform: rotate(90deg);
}

/* 历史对话空状态 */
.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #95a5a6;
}

.empty-icon {
  font-size: 36px;
  margin-bottom: 16px;
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
}

.empty-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  background: linear-gradient(90deg, #0056b3 0%, #3998fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.empty-hint {
  font-size: 13px;
  color: #7f8c8d;
  max-width: 200px;
  line-height: 1.5;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* 加载状态优化 */
.history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #7f8c8d;
  font-size: 14px;
  height: 100%;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(0, 123, 255, 0.1);
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
  position: relative;
}

.loading-spinner::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 50%;
  border-top-color: rgba(0, 123, 255, 0.5);
  animation: spin 2s linear infinite;
}

/* 历史对话加载更多状态 */
.history-loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  margin: 10px 12px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 123, 255, 0.1);
}

.history-loading-more .loading-spinner {
  margin-bottom: 8px;
}

/* 历史对话没有更多数据提示 */
.history-no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  margin: 10px 12px;
  // background: rgba(248, 249, 250, 0.8);
  border-radius: 10px;
  // border: 1px solid rgba(108, 117, 125, 0.1);
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}


.history-no-more span {
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 6px;
}

.history-no-more span::before {
  content: '📝';
  font-size: 16px;
  opacity: 0.7;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* 历史对话分组标题 */
.history-group-header {
  font-size: 12px;
  color: #8899aa;
  padding: 8px 12px 4px;
  margin-top: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.history-group-header:first-child {
  margin-top: 0;
}

/* 消息列表 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(165deg, #f8f9ff 0%, #f0f4fa 100%);
  background-image: radial-gradient(circle at 10px 10px, rgba(0, 123, 255, 0.03) 2px, transparent 0),
    radial-gradient(circle at 25px 25px, rgba(0, 123, 255, 0.02) 2px, transparent 0);
  background-size: 30px 30px;

}

.message {
  margin-bottom: 16px;
  display: flex;
  animation: fadeIn 0.3s ease-out;
}

.opening-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  justify-content: center;
}

.message-name {
  font-size: 24px;
  color: #000;
  font-weight: 600;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.message.user .message-content {
  // background: var(--primary-gradient);
  // color: white;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid rgba(0, 123, 255, 0.1);
  border-bottom-right-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.message.user .message-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.message.assistant .message-content {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  width: 80%;
  border: 1px solid rgba(0, 123, 255, 0.1);
  border-bottom-left-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.message-text-container {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
  flex: 1;
}

/* 消息中的文件显示 */
.message-files {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.message-file {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 123, 255, 1);
  border: 1px solid rgba(0, 123, 255, 1);
  border-radius: 6px;
  padding: 6px 10px;
  margin-bottom: 4px;
  font-size: 12px;
  max-width: 200px;
}



.message.user .message-file .file-icon {
  color: rgba(255, 255, 255, 1);
}

.message-file .file-name {
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.message.user .message-file .file-name {
  color: rgba(255, 255, 255, 0.9);
}

.message-file .file-size {
  color: #666;
  flex-shrink: 0;
}

.message.user .message-file .file-size {
  color: rgba(255, 255, 255, 0.7);
}

/* 流式消息光标动画 */
.streaming-cursor {
  display: inline-block;
  margin-left: 2px;
  color: #007bff;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

/* 思考过程样式 */
.thinking-section {
  margin-bottom: 12px;
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  background: #f8f9ff;
  overflow: hidden;
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #e3f2fd;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none;
}

.thinking-header:hover {
  background: #bbdefb;
}

.thinking-icon {
  margin-right: 8px;
  font-size: 16px;
}

.thinking-title {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #1976d2;
}

.thinking-status {
  color: #ff9800;
  font-weight: 400;
  // animation: pulse 1.5s ease-in-out infinite;
}

.thinking-toggle {
  font-size: 12px;
  color: #1976d2;
  transition: transform 0.2s;
}

.thinking-toggle.expanded {
  transform: rotate(0deg);
}

.thinking-content {
  padding: 12px;
  background: #fafafa;
  border-top: 1px solid #e3f2fd;
}

.thinking-text {
  font-size: 13px;
  line-height: 1.4;
  color: #555;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.thinking-placeholder {
  display: flex;
  align-items: center;
  padding: 12px;
  color: #666;
  font-style: italic;
}

.thinking-placeholder .thinking-dots {
  margin-right: 8px;
}

.thinking-placeholder-text {
  font-size: 13px;
}

/* 消息状态指示器 */
.message-status {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-top: 4px;
  opacity: 0.8;
}

/* 流式消息特殊样式 */
.message.streaming .message-content {
  position: relative;
}

.message.streaming.assistant .message-content {
  background: linear-gradient(90deg, #ffffff 0%, #f8f9ff 100%);
  border-left: 3px solid #007bff;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.message.assistant .message-time {
  text-align: left;
}

/* 加载动画 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #007bff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 思考状态样式 */
.thinking-indicator,
.loading-indicator {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

/* 暂停状态样式 */
.paused-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  padding: 8px 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
  font-size: 14px;
}

.paused-indicator svg {
  color: #f59e0b;
  flex-shrink: 0;
}

.paused-text {
  font-weight: 500;
}

.thinking-dots {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.thinking-dots span {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #007bff;
  margin: 0 1.5px;
  animation: thinking 1.6s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.thinking-label {
  font-size: 12px;
  color: #666;
  font-weight: 400;
  letter-spacing: 0.3px;
}

@keyframes thinking {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.4;
  }

  40% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 思考中的消息特殊样式 */
.message.thinking .message-content {
  background: #f8f9ff;
  border-left: 3px solid #bbdefb;
}

/* 输入框 */
.chat-input {
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid rgba(0, 123, 255, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

}

.chat-input.opening {
  margin: 25px auto !important;
}

.chat-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.2), transparent);
  z-index: -1;
}

.chat-input.drag-over {
  background-color: rgba(240, 248, 255, 0.95);
  border-top-color: #007bff;
  box-shadow: inset 0 0 20px rgba(0, 123, 255, 0.1);
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  position: relative;
  justify-content: end;
}

.input-container textarea {
  box-sizing: content-box;
  flex: 1;
  border: 1px solid rgba(0, 123, 255, 0);
  border-radius: 20px;
  padding: 12px 16px;
  resize: none;
  outline: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  max-height: 120px;
  transition: all 0.3s;
  background: rgba(255, 255, 255, 0.8);
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03), inset 0 1px 3px rgba(0, 0, 0, 0.02);
}


.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.send-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  pointer-events: none;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

/* 停止模式样式 */
.send-btn.stop-mode {
  width: auto;
  min-width: 60px;
  height: 32px;
  border-radius: 16px;
  background: #007bff;
  padding: 0 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.send-btn.stop-mode:hover {
  background: #0056b3;
  transform: scale(1.02);
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

/* 停止内容容器 */
.stop-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* Loading点动画 */
.loading-dot {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 停止文本 */
.stop-text {
  font-size: 12px;
  font-weight: 500;
  color: white;
  line-height: 1;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.file-upload-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.file-upload-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  pointer-events: none;
}

.file-upload-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 已上传文件列表样式优化 */
.uploaded-files {
  margin-bottom: 12px;
  border-radius: 10px;
  background: rgba(247, 248, 250, 0.7);
  padding: 10px;
  border: 1px solid #e9ecef;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eaedf0;
}

.files-count {
  font-size: 13px;
  color: #555;
  font-weight: 500;
}

.files-list {
  display: flex;
  // flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  max-width: 30%;
}

.file-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.file-icon-wrapper {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f4f9;
  flex-shrink: 0;
}

.file-icon-wrapper.document {
  background: #e3f2fd;
  color: #1976d2;
}

.file-icon-wrapper.image {
  background: #e8f5e9;
  color: #2e7d32;
}

.file-icon-wrapper.audio {
  background: #fff3e0;
  color: #e65100;
}

.file-icon-wrapper.video {
  background: #fce4ec;
  color: #c2185b;
}

.file-icon {
  width: 14px;
  height: 14px;
}

.file-details {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.file-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 11px;
  color: #888;
  margin-top: 2px;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}

.remove-file-btn:hover {
  background-color: #f8f9fa;
  color: #dc3545;
}

/* 文件上传按钮 */
.file-upload-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-upload-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  color: #007bff;
}

.file-upload-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 上传进度提示 */
.upload-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 13px;
  color: #0066cc;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}



.input-container textarea:disabled {
  // background-color: #f8f9fa;
  cursor: not-allowed;
}

.send-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background-color: #0056b3;
  transform: scale(1.05);
}

.send-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 遮罩层 */
.chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: -1;
  transition: background-color 0.3s ease;
}

.chat-overlay.expanded-overlay {
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 10000;
}



/* 滚动条样式 */
.chat-messages,
.history-list,
textarea {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

/* 开场白样式 */
.opening-statement {
  padding: 20px;
  max-width: 800px;
  margin: auto;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 123, 255, 0.1);
  animation: fadeIn 0.5s ease-out;
}

.popup-mode {
  width: 370px;
}

.opening-text {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 400;
  letter-spacing: 0.2px;
}

/* 推荐问题样式 */
.suggested-questions {
  margin-top: 16px;
}

.questions-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 10px;
  position: relative;
  padding-left: 18px;
}

.questions-title::before {
  content: '💡';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
}

.questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.question-item {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 18px;
  padding: 8px 14px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
  max-width: fit-content;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.question-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.question-item:hover {
  background-color: #e9f0ff;
  border-color: #c5d7f9;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.question-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 123, 255, 0.1);
}

.question-icon {
  font-size: 14px;
  opacity: 0.7;
  flex-shrink: 0;
}

.question-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

@media (max-width: 768px) {
  .opening-statement {
    margin: 10px;
    padding: 15px;
  }

  .question-item {
    padding: 6px 12px;
    font-size: 13px;
  }

  .question-text {
    max-width: 150px;
  }
}

/* 移动端扁平化样式 */
.mobile-flat {
  background: #ffffff;
  box-shadow: none !important;
  border: none !important;
}

/* 移动端顶部导航栏 */
.mobile-top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
  height: 56px;
  box-sizing: border-box;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  width: 48px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  text-align: center;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover, .nav-btn.active {
  background: #f3f4f6;
  color: #374151;
}

.nav-btn:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

/* 移动端功能菜单 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-top: 56px;
}

.mobile-menu {
  background: #ffffff;
  width: 280px;
  max-height: calc(100vh - 56px);
  border-radius: 0 0 16px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideInLeft 0.3s ease-out;
}

.menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.menu-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.menu-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.menu-items {
  padding: 8px 0;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border: none;
  background: transparent;
  color: #374151;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.menu-item:hover {
  background: #f3f4f6;
}

.menu-item:active {
  background: #e5e7eb;
}

.menu-item svg {
  color: #6b7280;
  flex-shrink: 0;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* AI免责声明 */
.ai-disclaimer {
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 0 16px 12px 16px;
  text-align: center;
  font-size: 12px;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.ai-disclaimer.mobile {
  margin: 0 16px 12px 16px;
}

/* 桌面端免责声明样式 */
@media (min-width: 769px) {
  .ai-disclaimer:not(.mobile) {
    margin: 0 0 12px 0;
    border-radius: 6px;
    font-size: 11px;
    padding: 6px 12px;
  }
}

/* 移动端输入框 */
.mobile-input-container {
  padding: 16px;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 8px;
  border: 1px solid #e5e7eb;
}

.mobile-textarea {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-size: 16px;
  line-height: 1.5;
  padding: 8px 12px;
  max-height: 120px;
  min-height: 20px;
  color: #1f2937;
}

.mobile-textarea::placeholder {
  color: #9ca3af;
}

.mobile-input-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.mobile-file-btn, .mobile-send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.mobile-file-btn {
  background: #e5e7eb;
  color: #6b7280;
}

.mobile-file-btn:hover:not(:disabled) {
  background: #d1d5db;
  color: #374151;
}

.mobile-file-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mobile-send-btn {
  background: #3b82f6;
  color: white;
}

.mobile-send-btn:hover:not(:disabled) {
  background: #2563eb;
}

.mobile-send-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.mobile-send-btn.stop-mode {
  background: #ef4444;
}

.mobile-send-btn.stop-mode:hover {
  background: #dc2626;
}

.mobile-stop-content {
  font-size: 12px;
  font-weight: 500;
}

/* 思考模式切换按钮 */
.thinking-mode-toggle {
  padding: 12px 16px;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
}

.thinking-mode-toggle.mobile {
  padding: 12px 16px;
}

.thinking-toggle-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.thinking-toggle-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.thinking-toggle-btn.active {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.thinking-toggle-btn svg {
  flex-shrink: 0;
}

/* 桌面端思考模式切换按钮样式 */
@media (min-width: 769px) {
  .thinking-mode-toggle:not(.mobile) {
    padding: 8px 0;
    border-top: 1px solid #e5e7eb;
    background: transparent;
  }

  .thinking-mode-toggle:not(.mobile) .thinking-toggle-btn {
    max-width: 300px;
    margin: 0 auto;
    font-size: 13px;
    padding: 8px 12px;
    border-radius: 6px;
  }
}

/* 移动端全屏样式 */
@media (max-width: 768px) {
  .chat-popup {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    animation: slideUpMobile 0.3s ease-out;
    display: flex;
    flex-direction: column;
  }

  .chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 16px;
  }

  .message {
    margin-bottom: 16px;
  }

  .message.user {
    align-self: flex-end;
  }

  .message.assistant {
    align-self: flex-start;
  }

  .message-content {
    max-width: 85%;
    word-wrap: break-word;
  }

  .user .message-content {
    background: #3b82f6;
    color: white;
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
    margin-left: auto;
  }

  .assistant .message-content {
    background: #f3f4f6;
    color: #1f2937;
    border-radius: 18px 18px 18px 4px;
    padding: 12px 16px;
  }

  .message-time {
    font-size: 11px;
    color: #9ca3af;
    margin-top: 4px;
    text-align: center;
  }

  .user .message-time {
    text-align: right;
  }

  .assistant .message-time {
    text-align: left;
  }
}

  .chat-bubble {
    width: 50px;
    height: 50px;
  }

  .chat-header {
    padding: 16px 20px;
    /* 为状态栏留出空间 */
    padding-top: max(16px, env(safe-area-inset-top));
  }

  /* 移动端历史对话样式 */
  .history-sidebar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 2;
    background: white;
  }

  /* 移动端开场白样式 */
  .opening-statement {
    margin: 16px;
    padding: 20px;
    border-radius: 16px;
    background: #f8f9fa;
    border: 1px solid #e5e7eb;
  }

  .opening-header {
    margin-bottom: 12px;
  }

  .opening-text {
    font-size: 15px;
    line-height: 1.6;
    color: #374151;
  }

  .suggested-questions {
    margin-top: 16px;
  }

  .questions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .question-item {
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    text-align: left;
    width: 100%;
    box-sizing: border-box;
  }

  /* 移动端文件上传样式 */
  .uploaded-files {
    margin: 0 16px 12px 16px;
    border-radius: 12px;
  }

  .file-item {
    padding: 12px;
    border-radius: 8px;
  }

  /* 移动端安全区域适配 */
  .mobile-top-nav {
    padding-top: max(12px, env(safe-area-inset-top));
    height: calc(56px + env(safe-area-inset-top));
  }

  .mobile-input-container {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }

  .thinking-mode-toggle.mobile {
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }

  /* 移动端滚动优化 */
  .chat-messages::-webkit-scrollbar {
    display: none;
  }

  .chat-messages {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  /* 移动端触摸反馈 */
  .nav-btn:active,
  .menu-item:active,
  .mobile-file-btn:active,
  .mobile-send-btn:active,
  .thinking-toggle-btn:active {
    transform: scale(0.95);
  }
}

.page-mode {
  .chat-wrapper {
    width: 1000px;
    margin: auto;
  }

  .chat-wrapper-mobile{
    width: 100%;
    margin: auto;
  }

  .chat-input {
    width: 1000px;
    margin: auto;
    border: 1px solid rgba(0, 123, 255, 0.1);
    margin-bottom: 20px;
    border-radius: 16px;
  }

  .chat-input-mobile{
    width: auto;
    margin: 0;
    border: 1px solid rgba(0, 123, 255, 0.1);
    margin-bottom: 0px;
    border-radius: 16px;
  }

  .input-container {
    flex-wrap: wrap;
  }
}
</style>
<style lang="less">
.markdown-body {
  max-width: 100%;

  ::-webkit-scrollbar {
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.markdown-body {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  li,
  ol,
  p,
  td,
  th,
  ul {
    margin-block-start: 0;
    margin-block-end: 0;
  }
}

.bot-body {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  li,
  ol,
  p,
  td,
  th,
  ul {
    margin-block-start: 0.5em;
    margin-block-end: 0.5em;
  }
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  border-collapse: collapse;
  border-spacing: 0;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #e8eaec;
}

.markdown-body table th {
  border: 1px solid #e8eaec;
  padding: 6px 13px;
  font-weight: 600;
  text-align: center;
  background-color: #f8f8f9;
}

.markdown-body table td {
  border: 1px solid #e8eaec;
  padding: 6px 13px;
  text-align: left;
}

/* 表格隔行变色 */
.markdown-body table tr:nth-child(2n) {
  background-color: #f8f8f9;
}

/* 代码块样式 */
.markdown-body pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 0px;
  margin-top: 0px;
}

.markdown-body pre code {
  background: transparent;
  padding: 0;
  white-space: pre;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  display: block;
  overflow-x: auto;
}

/* 行内代码样式 */
.markdown-body code {
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 3px;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
  margin: 0;
  color: #24292f;
}

/* Markdown 列表样式 */
.markdown-body ul,
.markdown-body ol {
  white-space: normal;
  padding-left: 24px;
}

.markdown-body ul {
  list-style-type: disc;
}

.markdown-body ul ul {
  list-style-type: circle;
}

.markdown-body ul ul ul {
  list-style-type: square;
}

.markdown-body ol {
  list-style-type: decimal;
}

.markdown-body blockquote {
  white-space: normal;
}

.markdown-body img {
  max-width: 100%;
}

/* 代码块容器样式 */
.code-block-container {
  margin: 16px 0;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  overflow: hidden;
  background: #fafbfc;
}

.code-block-header {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 8px 16px;
  border-bottom: 1px solid #e8eaec;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.code-language-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-block-container pre {
  margin: 0;
  background: transparent;
  border-radius: 0;
}

.code-block-container pre code {
  padding: 16px;
  display: block;
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0 0 8px 8px;
}

/* Echarts 容器样式 */
.echarts-container {
  margin: 16px 0;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  overflow: hidden;
}

/* 图表加载样式 */
.chart-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.8) 0%, rgba(240, 245, 255, 0.8) 100%);
  border-radius: 8px;
  border: 1px dashed rgba(0, 123, 255, 0.3);
}

.chart-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  border-top: 3px solid #007bff;
  animation: chart-spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  margin-bottom: 12px;
  position: relative;
}

.chart-loading-spinner::before,
.chart-loading-spinner::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
}

.chart-loading-spinner::before {
  border-top-color: rgba(0, 123, 255, 0.5);
  animation: chart-spin 2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.chart-loading-spinner::after {
  border-top-color: rgba(0, 123, 255, 0.2);
  animation: chart-spin 3s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.chart-loading-text {
  font-size: 14px;
  color: #0056b3;
  font-weight: 500;
  background: linear-gradient(90deg, #0056b3 0%, #3998fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse-text 1.5s ease-in-out infinite;
}

@keyframes chart-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-text {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

/* 图表错误样式 */
.echarts-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(255, 235, 235, 0.7);
  color: #dc3545;
  font-size: 14px;
  border-radius: 8px;
  border: 1px dashed rgba(220, 53, 69, 0.5);
  padding: 20px;
}

.echarts-error::before {
  content: '⚠️';
  font-size: 18px;
  margin-right: 8px;
}
</style>
